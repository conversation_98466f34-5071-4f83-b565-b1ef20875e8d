import { Entity, EntityComponentTypes, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../general_mechanics/targetUtils";
import { getDirection } from "../../../utilities/vector3";
import { faceDirection } from "../../../entities/projectileRotation";

/**
 * Attack timing constants for book of the damned attack phases
 */
const DAMAGE_START_TIMING = 70; // Start firing skulls at tick 70 (3.5 seconds)
const DAMAGE_END_TIMING = 150; // End firing skulls at tick 150 (7.5 seconds)

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 200; // 10 seconds (same as cataclysm)

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Configuration for the book of the damned attack
 */
const BOOK_OF_THE_DAMNED_CONFIG = {
  /** Projectile entity type */
  PROJECTILE_TYPE: "ptd_dbb:flying_skull",
  /** Projectile speed (velocity multiplier) */
  PROJECTILE_SPEED: 0.3,
  /** Tick interval for applying impulse */
  IMPULSE_INTERVAL: 1,
  /** Vertical offset from necromancer position */
  VERTICAL_OFFSET: 1.5,
  /** Delay between firing skulls in ticks */
  SKULL_FIRE_DELAY: 5 // Fire every 5 ticks during damage phase
};

/**
 * Executes the book of the damned attack for the Necromancer using the new timing system
 * Uses localized runTimeout for skull firing phase, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeBookOfTheDamnedAttack(necromancer: Entity): void {
  // Start skull firing phase at tick 70
  let skullFiringInterval: number;

  let damageStartTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageStartTiming);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "book_of_the_damned") {
        // Start continuous skull firing phase
        skullFiringInterval = system.runInterval(() => {
          try {
            const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
            if (isDead || necromancer.getProperty("ptd_dbb:attack") !== "book_of_the_damned") {
              system.clearRun(skullFiringInterval);
              return;
            }

            fireSkull(necromancer);
          } catch (error) {
            system.clearRun(skullFiringInterval);
          }
        }, BOOK_OF_THE_DAMNED_CONFIG.SKULL_FIRE_DELAY);

        // Stop skull firing at the end of damage phase
        system.runTimeout(() => {
          if (skullFiringInterval) {
            system.clearRun(skullFiringInterval);
          }
        }, DAMAGE_END_TIMING - DAMAGE_START_TIMING);
      }
    } catch (error) {
      system.clearRun(damageStartTiming);
    }
  }, DAMAGE_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (skullFiringInterval) system.clearRun(skullFiringInterval);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "book_of_the_damned") {
        necromancer.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      necromancer.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Fires a single skull projectile at the target
 * @param necromancer The necromancer entity
 */
function fireSkull(necromancer: Entity): void {
  try {
    // Find the target to determine direction
    const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
    if (!target) return;

    const headLoc = necromancer.getHeadLocation();
    const viewDir = necromancer.getViewDirection();

    // Spawn the flying skull projectile at the location of the necromancer's book
    const spawnPos: Vector3 = {
      x: headLoc.x + viewDir.x * 2.3,
      y: headLoc.y + BOOK_OF_THE_DAMNED_CONFIG.VERTICAL_OFFSET,
      z: headLoc.z + viewDir.z * 2.3
    };

    // Get the target location with a y-offset
    const targetLoc: Vector3 = {
      x: target.getHeadLocation().x,
      y: target.getHeadLocation().y - 0.2,
      z: target.getHeadLocation().z
    };

    // Calculate direction toward the target (already normalized)
    const direction = getDirection(spawnPos, targetLoc);

    // Calculate velocity based on direction and speed
    const velocity: Vector3 = {
      x: direction.x * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      y: direction.y * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      z: direction.z * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED
    };

    // Spawn the flying skull entity
    const projectile = necromancer.dimension.spawnEntity(BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_TYPE, spawnPos);

    if (!projectile) return;

    // Apply initial impulse
    const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
    if (!projectileComponent) return;

    // Apply initial impulse
    projectileComponent.shoot(velocity);

    // Set up interval to continuously apply impulse in the SAME direction
    const intervalId = system.runInterval(() => {
      try {
        // Check if entity is still valid/exists
        if (!projectile) {
          system.clearRun(intervalId);
          return;
        }

        // Apply impulse again using the SAME velocity vector
        const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
        if (projectileComponent) {
          projectileComponent.shoot(velocity);
        }

        // Constantly face toward the direction
        faceDirection(projectile, spawnPos, targetLoc);
      } catch (error) {
        // If any error occurs (likely because projectile no longer exists)
        system.clearRun(intervalId);
      }
    }, BOOK_OF_THE_DAMNED_CONFIG.IMPULSE_INTERVAL);

    // Play a sound effect for each skull fired
    necromancer.dimension.playSound("mob.blaze.shoot", headLoc);
  } catch (error) {
    console.warn(`Error in book of the damned attack: ${error}`);
  }
}
