{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_necromancer.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"walking": "q.ground_speed > 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"cataclysm": "q.property('ptd_dbb:attack') == 'cataclysm'"}, {"book_of_the_damned": "q.property('ptd_dbb:attack') == 'book_of_the_damned'"}, {"soul_drain": "q.property('ptd_dbb:attack') == 'soul_drain'"}, {"phantom_phase_start": "q.property('ptd_dbb:attack') == 'phantom_phase_start'"}, {"undead_summon": "q.property('ptd_dbb:attack') == 'undead_summon'"}, {"arcane_blast": "q.property('ptd_dbb:attack') == 'arcane_blast'"}, {"soul_hands": "q.property('ptd_dbb:attack') == 'soul_hands'"}, {"soul_trap": "q.property('ptd_dbb:attack') == 'soul_trap'"}], "blend_transition": 0.3}, "walking": {"animations": ["move"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"cataclysm": "q.property('ptd_dbb:attack') == 'cataclysm'"}, {"soul_drain": "q.property('ptd_dbb:attack') == 'soul_drain'"}, {"phantom_phase_start": "q.property('ptd_dbb:attack') == 'phantom_phase_start'"}, {"undead_summon": "q.property('ptd_dbb:attack') == 'undead_summon'"}, {"arcane_blast": "q.property('ptd_dbb:attack') == 'arcane_blast'"}, {"soul_hands": "q.property('ptd_dbb:attack') == 'soul_hands'"}, {"soul_trap": "q.property('ptd_dbb:attack') == 'soul_trap'"}], "blend_transition": 0.3}, "cataclysm": {"animations": ["cataclysm"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "book_of_the_damned": {"animations": ["book_of_the_damned"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "soul_drain": {"animations": ["soul_drain"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "phantom_phase_start": {"animations": ["phantom_phase_start"], "transitions": [{"phantom_phase_end": "q.property('ptd_dbb:attack') == 'phantom_phase_end'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "phantom_phase_end": {"animations": ["phantom_phase_end"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "undead_summon": {"animations": ["undead_summon"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "arcane_blast": {"animations": ["arcane_blast"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "soul_hands": {"animations": ["soul_hands"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "soul_trap": {"animations": ["soul_trap"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}